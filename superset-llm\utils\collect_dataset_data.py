import os
import psycopg2
import numpy as np
import pandas as pd
import concurrent.futures
from datetime import datetime
from dotenv import load_dotenv
from multiprocessing import cpu_count
from psycopg2.extras import RealDictCursor
from langchain_core.documents import Document
from concurrent.futures import ProcessPoolExecutor
from langchain_postgres.vectorstores import PGVector

load_dotenv()

SECRET_KEY = os.getenv("SUPERSET_SECRET_KEY")
is_docker = os.getenv("IS_DOCKER", "false").lower() == "true"
host = "db" if is_docker else "localhost"

connection_string = os.getenv("VECTOR_DB")


def __get_dataset_by_id(dataset_id, connection):
    query = """
        SELECT id, table_name, database_id, perm, schema_perm
        FROM tables
        WHERE id = %s;
    """

    try:
        with connection.cursor(cursor_factory=RealDictCursor) as cursor:
            cursor.execute(query, (dataset_id,))
            result = cursor.fetchone()
            return result

    except psycopg2.Error as e:
        connection.rollback()
        print(f"An error occurred while querying the database: {e}")
        return None


def __get_databse(database_id, connection):
    query = """
        SELECT id, database_name, sqlalchemy_uri, password
        FROM dbs
        WHERE id = %s;
    """

    try:
        with connection.cursor(cursor_factory=RealDictCursor) as cursor:
            cursor.execute(query, (database_id,))
            result = cursor.fetchone()
            return result

    except psycopg2.Error as e:
        connection.rollback()
        print(f"An error occurred while querying the database: {e}")
        return None


def dataset_dimensions(table_name, database_connection):
    query_count = f'SELECT COUNT(*) FROM "{table_name}"'
    with database_connection.cursor() as cursor:
        cursor.execute(query_count)
        total_rows = cursor.fetchone()[0]
    return total_rows


def describe_data_and_generate_insights(data_chunks):
    data = pd.DataFrame(data_chunks)

    insights = []
    insights.append(
        Document(
            f"The dataset contains {data.shape[0]} rows and {data.shape[1]} columns."
        )
    )

    for column in data.columns:
        col_data = data[column]
        col_type = col_data.dtype

        if col_type == "object":
            unique_values = col_data.nunique()
            most_common_value = (
                col_data.mode()[0] if not col_data.mode().empty else None
            )
            insights.append(
                Document(
                    f"Column '{column}' is categorical with {unique_values} unique values. "
                    f"The most common value is '{most_common_value}'."
                )
            )
        elif np.issubdtype(col_type, np.number):
            col_mean = col_data.mean()
            col_median = col_data.median()
            col_std = col_data.std()
            col_min = col_data.min()
            col_max = col_data.max()
            insights.append(
                Document(
                    f"Column '{column}' is numerical. Mean: {col_mean:.2f}, Median: {col_median:.2f}, "
                    f"Standard Deviation: {col_std:.2f}, Min: {col_min}, Max: {col_max}."
                )
            )
        elif np.issubdtype(col_type, np.datetime64):
            col_min = col_data.min()
            col_max = col_data.max()
            insights.append(
                Document(
                    f"Column '{column}' is a datetime column with a range from {col_min} to {col_max}."
                )
            )
        else:
            pass

    null_counts = data.isnull().sum().sum()
    if null_counts > 0:
        insights.append(
            Document(
                f"The dataset contains {null_counts} missing values across all columns."
            )
        )
    else:
        insights.append(Document("The dataset has no missing values."))

    return insights


def fetch_chunk_parallel(table_name, offset, limit, connection):
    query = f'SELECT * FROM "{table_name}" LIMIT {limit} OFFSET {offset}'
    with connection.cursor(cursor_factory=RealDictCursor) as cursor:
        cursor.execute(query)
        return cursor.fetchall()


def fetch_data_parallel(dataset_id, connection, chunk_size=1000):
    dataset = __get_dataset_by_id(dataset_id, connection)
    database = __get_databse(dataset["database_id"], connection)

    example_db = {
        "dbname": database["database_name"].lower(),
        "user": "superset",
        "password": "superset",
        "host": host,
        "port": 5432,
    }
    database_connection = psycopg2.connect(**example_db)
    table_name = dataset["table_name"]
    total_rows = dataset_dimensions(table_name, database_connection)

    offsets = range(0, total_rows, chunk_size)
    results = []

    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = {
            executor.submit(
                fetch_chunk_parallel,
                table_name,
                offset,
                chunk_size,
                database_connection,
            ): offset
            for offset in offsets
        }
        for future in concurrent.futures.as_completed(futures):
            results.extend(future.result())

    return results


def preprocess_documents(rows):
    return [
        Document(" ".join(f"{key}:{value}" for key, value in row.items()))
        for row in rows
    ]


def parallel_preprocess_documents(rows, batch_size=1000):
    batches = [rows[i : i + batch_size] for i in range(0, len(rows), batch_size)]

    with ProcessPoolExecutor(max_workers=cpu_count()) as executor:
        results = list(executor.map(preprocess_documents, batches))

    return [doc for batch in results for doc in batch]


def process_dataset_combined(dataset_id, connection, embedding_model):
    data_chunks = fetch_data_parallel(dataset_id, connection)

    collection_name = f"dataset_{dataset_id}"

    insights = describe_data_and_generate_insights(data_chunks)

    preprocess_docs = parallel_preprocess_documents(data_chunks)
    preprocess_docs.extend(insights)

    vector_store = PGVector.from_documents(
        documents=preprocess_docs,
        embedding=embedding_model,
        collection_name=collection_name,
        connection=connection_string,
        use_jsonb=True,
    )

    return (
        f"The connection between dataset {dataset_id} and LLM is successfully established.",
        len(data_chunks),
    )


def check_embeddings_status(dataset_id, connection):
    dataset_id = str(dataset_id)
    query = "SELECT embeddings_created FROM dataset_metadata WHERE dataset_id = %s"
    with connection.cursor(cursor_factory=RealDictCursor) as cursor:
        cursor.execute(query, (dataset_id,))
        result = cursor.fetchone()
        return result["embeddings_created"] if result else None


def update_embeddings_status(dataset_id, status, connection, document_length):
    query = """
    INSERT INTO dataset_metadata (dataset_id, embeddings_created, updated_at, document_length)
    VALUES (%s, %s, %s, %s)
    ON CONFLICT (dataset_id)
    DO UPDATE SET embeddings_created = %s, updated_at = %s, document_length = %s
    """
    now = datetime.utcnow()
    with connection.cursor() as cursor:
        cursor.execute(
            query,
            (
                dataset_id,
                status,
                now,
                document_length,
                status,
                now,
                str(document_length),
            ),
        )
        connection.commit()


def delete_embeddings(dataset_id, connection):
    query = "DELETE FROM dataset_metadata WHERE dataset_id = %s"

    try:
        with connection.cursor() as cursor:
            cursor.execute(query, (dataset_id,))
            connection.commit()
            if cursor.rowcount > 0:
                return f"Row with dataset_id {dataset_id} was successfully deleted."
            else:
                return f"No row found with dataset_id {dataset_id}."
    except Exception as e:
        connection.rollback()
        return f"An error occurred while deleting the row: {e}"


def get_embeddings_len(dataset_id, connection):
    dataset_id = str(dataset_id)
    query = "SELECT document_length FROM dataset_metadata WHERE dataset_id = %s"
    with connection.cursor(cursor_factory=RealDictCursor) as cursor:
        cursor.execute(query, (dataset_id,))
        result = cursor.fetchone()
        return int(result["document_length"]) if result else None
