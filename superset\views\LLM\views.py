from superset.views.base import (
    BaseSupersetView,
    common_bootstrap_payload,
    DeleteMixin,
    deprecated,
    generate_download_headers,
    SupersetModelView,
)

from flask_appbuilder import BaseView, expose, Model, ModelView

from flask_appbuilder import expose


class PreferencesModelView(BaseSupersetView):
    route_base = "/preferences"

    @expose("/model/")
    # @has_access
    def list(self):
        # Renders a static template
        return super().render_app_template()


class ComingSoonView(BaseSupersetView):
    route_base = "/coming-soon"

    @expose("/")
    def list(self):
        # print("Page Name:", page_name)
        # return self.render_template("superset/spa.html", entry="spa")
        return super().render_app_template()
        # return "Hello"

class OrchestrationView(BaseSupersetView):
    route_base = "/orchestration"
 
    @expose("/")
    # @has_access
    def list(self):
        # Renders a static template
        return super().render_app_template()
    
class PythonLabView(BaseSupersetView):
    route_base = "/python-lab"
 
    @expose("/")
    # @has_access
    def list(self):
        # Renders a static template
        return super().render_app_template()
class PythonSavedQueriesView(BaseSupersetView):
    route_base = "/python-saved-queries"
 
    @expose("/")
    # @has_access
    def list(self):
        # Renders a static template
        return super().render_app_template()

class EtlToolsViewList(BaseSupersetView):
    route_base = "/etl-tools/list"

    @expose("/")
    # @has_access
    def list(self):
        # Renders a static template
        return super().render_app_template()

class NewETLToolsView(BaseSupersetView):
    route_base = "/etl-tools"

    @expose("/new")
    # @has_access
    def list(self):
        # Renders a static template
        return super().render_app_template()

class CustomLogoView(BaseSupersetView):
    route_base = "/custom-logo"

    @expose("/")
    # @has_access
    def list(self):
        # Renders a static template
        return super().render_app_template()

class CustomAIView(BaseSupersetView):
    route_base = "/custom-ai"

    @expose("/")
    # @has_access
    def list(self):
        # Renders a static template
        return super().render_app_template()


class AboutView(BaseSupersetView):
    route_base = "/about"

    @expose("/")
    # @has_access
    def list(self):
        # Renders a static template
        return super().render_app_template()


class ModeView(BaseSupersetView):
    route_base = "/mode"

    @expose("/")
    # @has_access
    def list(self):
        # Renders a static template
        return super().render_app_template()


class ModeView(BaseSupersetView):
    route_base = "/orchestration"

    @expose("/")
    # @has_access
    def list(self):
        # Renders a static template
        return super().render_app_template()
