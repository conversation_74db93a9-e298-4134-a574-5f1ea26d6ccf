import ast
import pandas as pd
import numpy as np
import scipy as sp
from typing import Dict, Any, Optional, List, Tuple
import uuid
import traceback
from datetime import datetime

from python_lab.lab.python_validator import PythonValidator, PythonValidationAnnotation


class PythonExecutionResult:
    """Container for Python execution results"""
    
    def __init__(
        self,
        success: bool,
        data: Optional[pd.DataFrame] = None,
        error: Optional[str] = None,
        traceback: Optional[str] = None,
        execution_time: Optional[float] = None
    ):
        self.success = success
        self.data = data
        self.error = error
        self.traceback = traceback
        self.execution_time = execution_time
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert execution result to dictionary"""
        result = {
            "success": self.success,
            "execution_time": self.execution_time
        }
        
        if self.data is not None:
            # Convert DataFrame to records for JSON serialization
            result["data"] = self.data.to_dict(orient="records")
            result["columns"] = list(self.data.columns)
            result["row_count"] = len(self.data)
        
        if self.error:
            result["error"] = self.error
            
        if self.traceback:
            result["traceback"] = self.traceback
            
        return result


class PythonExecutor:
    """Execute Python code with safety measures and return results"""
    
    def __init__(self, db_connection=None):
        self.db_connection = db_connection
        self.validator = PythonValidator()
        self.allowed_modules = {
            "pandas": pd,
            "numpy": np,
            "scipy": sp,
            "pd": pd,
            "np": np,
            "sp": sp,
        }
    
    def execute(self, python_code: str, context: Dict[str, Any] = None) -> PythonExecutionResult:
        """
        Execute Python code and return the results
        
        Args:
            python_code: The Python code to execute
            context: Additional variables to include in execution context
            
        Returns:
            PythonExecutionResult object containing execution results
        """
        import time
        
        # First validate the code
        validation_results = self.validator.validate(python_code)
        if validation_results:
            # Code has syntax errors
            return PythonExecutionResult(
                success=False,
                error="Syntax error in Python code",
                traceback="\n".join([str(result.to_dict()) for result in validation_results])
            )
        
        # Prepare execution environment
        execution_globals = self.allowed_modules.copy()
        if context:
            execution_globals.update(context)
        
        # Track execution time
        start_time = time.time()
        
        try:
            # Execute the code
            local_vars = {}
            exec(python_code, execution_globals, local_vars)
            
            # Look for a result DataFrame
            result_df = None
            for var_name, var_value in local_vars.items():
                if isinstance(var_value, pd.DataFrame):
                    result_df = var_value
                    break
            
            execution_time = time.time() - start_time
            
            if result_df is not None:
                return PythonExecutionResult(
                    success=True,
                    data=result_df,
                    execution_time=execution_time
                )
            else:
                return PythonExecutionResult(
                    success=True,
                    error="No DataFrame result found in execution output",
                    execution_time=execution_time
                )
                
        except Exception as e:
            execution_time = time.time() - start_time
            return PythonExecutionResult(
                success=False,
                error=str(e),
                traceback=traceback.format_exc(),
                execution_time=execution_time
            )