from flask import Blueprint, request, jsonify
from datetime import datetime
import uuid
import pandas as pd

from utils.common import superset_connection
from python_lab.lab.python_validator import PythonValidator
from python_lab.lab.python_executor import PythonExecutor

tabs_bp = Blueprint("tabs_bp", __name__)


@tabs_bp.route("/save_query", methods=["POST"])
def save_query():
    """
    Create a new saved Python query tab.

    Expected payload:
    {
        "catalog": "string",
        "db_id": "integer",
        "description": "string",
        "label": "string",
        "python": "string"
    }
    """
    try:
        # Get JSON payload
        data = request.json
        if not data:
            return jsonify({"error": "No JSON payload provided"}), 400

        # Extract fields
        catalog = data.get("catalog")
        db_id = data.get("db_id")
        description = data.get("description", "")
        label = data.get("label")
        python_code = data.get("python")

        # Validate required fields
        if not all([db_id, label, python_code]):
            return jsonify({"error": "db_id, label, and python are required"}), 400

        # Generate UUID for the record
        record_uuid = str(uuid.uuid4())
        current_time = datetime.now()

        # TODO: Replace with actual user ID from session/auth
        # For now using placeholder values
        user_id = 1  # Replace with actual user ID from authentication
        created_by_fk = 1  # Replace with actual user ID
        changed_by_fk = 1  # Replace with actual user ID

        # Insert into database
        insert_query = """
        INSERT INTO saved_python_query (
            created_on, changed_on, user_id, db_id, label,
            description, changed_by_fk, created_by_fk, python,
            uuid, catalog
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        ) RETURNING id;
        """

        with superset_connection.cursor() as cursor:
            cursor.execute(
                insert_query,
                (
                    current_time,  # created_on
                    current_time,  # changed_on
                    user_id,  # user_id
                    db_id,  # db_id
                    label,  # label
                    description,  # description
                    changed_by_fk,  # changed_by_fk
                    created_by_fk,  # created_by_fk
                    python_code,  # python (storing Python code in python field)
                    record_uuid,  # uuid
                    catalog,  # catalog
                ),
            )

            # Get the inserted record ID
            record_id = cursor.fetchone()[0]

        superset_connection.commit()

        # Return success response
        return (
            jsonify(
                {
                    "success": True,
                    "message": "Tab created successfully",
                    "data": {
                        "id": record_id,
                        "uuid": record_uuid,
                        "catalog": catalog,
                        "db_id": db_id,
                        "description": description,
                        "label": label,
                        "python": python_code,
                        "created_on": current_time.isoformat(),
                        "changed_on": current_time.isoformat(),
                    },
                }
            ),
            201,
        )

    except Exception as e:
        superset_connection.rollback()
        return jsonify({"error": "Database error occurred", "details": str(e)}), 500


@tabs_bp.route("/validate", methods=["POST"])
def validate_python():
    """
    Validate Python code syntax.

    Expected payload:
    {
        "python": "string"
    }

    Returns:
    {
        "success": true,
        "result": [
            {
                "line_number": integer or null,
                "start_column": integer or null,
                "end_column": integer or null,
                "message": "string"
            }
        ]
    }
    """
    try:
        # Get JSON payload
        data = request.json
        if not data:
            return jsonify({"error": "No JSON payload provided"}), 400

        # Extract Python code
        python_code = data.get("python", "")
        if not python_code:
            return jsonify({"error": "python code is required"}), 400

        # Validate the Python code
        validator = PythonValidator()
        validation_results = validator.validate(python_code)

        # Convert validation results to dictionaries
        result = [annotation.to_dict() for annotation in validation_results]

        # Return success response
        return jsonify({"success": True, "result": result}), 200

    except Exception as e:
        return jsonify({"error": "Validation error occurred", "details": str(e)}), 500


@tabs_bp.route("/execute", methods=["POST"])
def execute_python():
    """
    Execute Python code and return results.

    Expected payload:
    {
        "python": "string",
        "db_id": integer,
        "catalog": "string",
        "schema": "string"
    }

    Returns:
    {
        "success": true,
        "data": [...],
        "columns": [...],
        "row_count": integer,
        "execution_time": float
    }
    """
    try:
        # Get JSON payload
        data = request.json
        if not data:
            return jsonify({"error": "No JSON payload provided"}), 400

        # Extract Python code
        python_code = data.get("python", "")
        if not python_code:
            return jsonify({"error": "python code is required"}), 400

        # Execute the Python code
        executor = PythonExecutor(db_connection=superset_connection)
        result = executor.execute(python_code)

        # Return the execution result
        return jsonify(result.to_dict()), 200 if result.success else 500

    except Exception as e:
        return jsonify({"error": "Execution error occurred", "details": str(e)}), 500


@tabs_bp.route("/save_dataset", methods=["POST"])
def save_dataset():
    """
    Save execution result as a new dataset/table.

    Expected payload:
    {
        "python": "string",
        "db_id": integer,
        "catalog": "string",
        "schema": "string",
        "table_name": "string",
        "if_exists": "fail|replace|append"
    }

    Returns:
    {
        "success": true,
        "message": "Dataset created successfully",
        "table_name": "string"
    }
    """
    try:
        # Get JSON payload
        data = request.json
        if not data:
            return jsonify({"error": "No JSON payload provided"}), 400

        # Extract fields
        python_code = data.get("python")
        db_id = data.get("db_id")
        catalog = data.get("catalog")
        schema = data.get("schema")
        table_name = data.get("table_name")
        if_exists = data.get("if_exists", "fail")

        # Validate required fields
        if not all([python_code, db_id, table_name]):
            return jsonify({"error": "python, db_id, and table_name are required"}), 400

        # Execute the Python code to get the DataFrame
        executor = PythonExecutor(db_connection=superset_connection)
        result = executor.execute(python_code)

        if not result.success or result.data is None:
            return (
                jsonify(
                    {
                        "error": "Failed to execute Python code",
                        "details": result.error or "No DataFrame result found",
                    }
                ),
                500,
            )

        # Get database connection string for the specified db_id
        db_query = "SELECT sqlalchemy_uri FROM dbs WHERE id = %s"
        with superset_connection.cursor() as cursor:
            cursor.execute(db_query, (db_id,))
            db_result = cursor.fetchone()
            if not db_result:
                return jsonify({"error": f"Database with id {db_id} not found"}), 404

            sqlalchemy_uri = db_result[0]

        # Save DataFrame to database
        from sqlalchemy import create_engine

        engine = create_engine(sqlalchemy_uri)

        # Construct schema.table_name if schema is provided
        full_table_name = f"{schema}.{table_name}" if schema else table_name

        # Save DataFrame to database
        result.data.to_sql(
            name=full_table_name, con=engine, if_exists=if_exists, index=False
        )

        # Return success response
        return (
            jsonify(
                {
                    "success": True,
                    "message": "Dataset created successfully",
                    "table_name": full_table_name,
                }
            ),
            201,
        )

    except Exception as e:
        return jsonify({"error": "Failed to save dataset", "details": str(e)}), 500


@tabs_bp.route("/get_query", methods=["GET"])
def get_query():
    """
    Get a saved Python query by ID.

    Expected query parameters:
    - id: integer
    """
    try:
        query_id = request.args.get("id")
        if not query_id:
            return jsonify({"error": "Query ID is required"}), 400

        # Query the database
        select_query = """
        SELECT * FROM saved_python_query WHERE id = %s
        """

        with superset_connection.cursor() as cursor:
            cursor.execute(select_query, (query_id,))
            result = cursor.fetchone()

            if not result:
                return jsonify({"error": f"Query with ID {query_id} not found"}), 404

            # Convert result to dictionary
            columns = [desc[0] for desc in cursor.description]
            query_data = dict(zip(columns, result))

            # Format datetime objects
            for key, value in query_data.items():
                if isinstance(value, datetime):
                    query_data[key] = value.isoformat()

        return jsonify({"success": True, "data": query_data}), 200

    except Exception as e:
        return jsonify({"error": "Failed to retrieve query", "details": str(e)}), 500
