/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

import axios, { AxiosResponse } from 'axios';
import React from 'react';

export type FetchValidationQueryParams = {
  dbId?: string | number;
  catalog?: string | null;
  schema?: string;
  python: string;
  templateParams?: string;
};

export type ValidationResult = {
  end_column: number | null;
  line_number: number | null;
  message: string | null;
  start_column: number | null;
};

export type ValidationResponse = {
  result: ValidationResult[];
};

/**
 * Validates Python query using Axios
 * @param params - Query validation parameters
 * @returns Promise with validation results
 */
export const validatePythonQuery = async (
  params: FetchValidationQueryParams,
): Promise<ValidationResult[]> => {
  const { dbId, catalog, schema, python, templateParams } = params;

  let template_params = templateParams;
  try {
    template_params = JSON.parse(templateParams || '');
  } catch (e) {
    template_params = undefined;
  }

  const postPayload = {
    catalog,
    schema,
    python,
    ...(template_params && { template_params }),
  };

  try {
    const response: AxiosResponse<ValidationResponse> = await axios.post(
      `${process.env.REACT_APP_LLM_URL}tabs/validate`,
      postPayload,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );

    return response.data.result;
  } catch (error) {
    console.error('Error validating Python query:', error);
    throw error;
  }
};

/**
 * Hook-like function for React components (optional)
 * This mimics the behavior of the original RTK Query hook
 */
export const usePythonQueryValidations = () => {
  const validateQuery = async (params: FetchValidationQueryParams) => {
    return validatePythonQuery(params);
  };

  return {
    validateQuery,
  };
};

/**
 * RTK Query compatible hook that matches the original export name
 * This provides the same interface as the original usePythonQueryValidationsQuery
 */
export const usePythonQueryValidationsQuery = (
  params: FetchValidationQueryParams,
  options?: {
    skip?: boolean;
    selectFromResult?: (result: {
      isLoading: boolean;
      isError: boolean;
      error: any;
      data: ValidationResult[] | undefined;
    }) => any;
  },
) => {
  const [data, setData] = React.useState<ValidationResult[] | undefined>(
    undefined,
  );
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<any>(null);
  const [isError, setIsError] = React.useState(false);

  // Memoize params to prevent infinite re-renders
  const memoizedParams = React.useMemo(
    () => params,
    [
      params.dbId,
      params.catalog,
      params.schema,
      params.python,
      params.templateParams,
    ],
  );

  const refetch = React.useCallback(async () => {
    if (options?.skip) return;

    setIsLoading(true);
    setError(null);
    setIsError(false);

    try {
      const result = await validatePythonQuery(memoizedParams);
      setData(result);
    } catch (err) {
      setError(err);
      setIsError(true);
    } finally {
      setIsLoading(false);
    }
  }, [memoizedParams, options?.skip]);

  React.useEffect(() => {
    if (!options?.skip && memoizedParams.python) {
      refetch();
    }
  }, [
    memoizedParams.python,
    memoizedParams.dbId,
    memoizedParams.catalog,
    memoizedParams.schema,
    memoizedParams.templateParams,
    options?.skip,
  ]);

  const baseResult = {
    data,
    isLoading,
    error,
    isError,
    refetch,
  };

  // If selectFromResult is provided, use it to transform the result
  if (options?.selectFromResult) {
    return options.selectFromResult(baseResult);
  }

  return baseResult;
};
