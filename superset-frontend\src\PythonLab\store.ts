/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
import {
  configureStore,
  ConfigureStoreOptions,
  StoreEnhancer,
} from '@reduxjs/toolkit';
import thunk from 'redux-thunk';
import { api } from 'src/hooks/apiResources/queryApi';
import messageToastReducer from 'src/components/MessageToasts/reducers';
import { getBootstrapData } from 'src/utils/getBootstrapData';
import { nanoid } from '@reduxjs/toolkit';
import pythonLabReducer from './reducers/pythonLab';
import getInitialState from './reducers/getInitialState';
import { persistPythonLabStateEnhancer } from './middlewares/persistSqlLabStateEnhancer';
import type { PythonLabRootState } from './types';

// Some reducers don't do anything, and redux is just used to reference the initial "state".
// This may change later, as the client application takes on more responsibilities.
const noopReducer =
  <STATE = unknown>(initialState: STATE) =>
  (state: STATE = initialState) =>
    state;

const bootstrapData = getBootstrapData();

export const USER_LOADED = 'USER_LOADED';

export type UserLoadedAction = {
  type: typeof USER_LOADED;
  user: any;
};

export const userReducer = (
  user = bootstrapData.user || {},
  action: UserLoadedAction,
): any => {
  if (action.type === USER_LOADED) {
    return action.user;
  }
  return user;
};

const reducers = {
  pythonLab: pythonLabReducer,
  localStorageUsageInKilobytes: noopReducer(0),
  messageToasts: messageToastReducer,
  common: noopReducer(bootstrapData.common),
  user: userReducer,
  impressionId: noopReducer(nanoid()),
};

const getMiddleware: ConfigureStoreOptions['middleware'] =
  getDefaultMiddleware =>
    [thunk, api.middleware];

export function setupPythonLabStore({
  disableDebugger = false,
  initialState = getInitialState(bootstrapData),
  rootReducers = reducers,
  ...overrides
}: {
  disableDebugger?: boolean;
  initialState?: ConfigureStoreOptions['preloadedState'];
  rootReducers?: ConfigureStoreOptions['reducer'];
} & Partial<ConfigureStoreOptions> = {}) {
  return configureStore({
    preloadedState: initialState,
    reducer: {
      [api.reducerPath]: api.reducer,
      ...rootReducers,
    },
    middleware: getMiddleware,
    devTools: process.env.WEBPACK_MODE === 'development' && !disableDebugger,
    enhancers: [persistPythonLabStateEnhancer as StoreEnhancer],
    ...overrides,
  });
}

export const pythonLabStore = setupPythonLabStore();
export type PythonLabStoreType = ReturnType<typeof pythonLabStore.getState>;
