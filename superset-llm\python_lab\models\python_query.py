from datetime import datetime
from typing import Optional, Dict, Any


class PythonQuery:
    """Model representing a Python Lab query"""
    
    def __init__(
        self,
        id: Optional[int] = None,
        uuid: Optional[str] = None,
        user_id: Optional[int] = None,
        db_id: Optional[int] = None,
        label: Optional[str] = None,
        description: Optional[str] = None,
        python: Optional[str] = None,
        catalog: Optional[str] = None,
        schema: Optional[str] = None,
        created_on: Optional[datetime] = None,
        changed_on: Optional[datetime] = None,
        created_by_fk: Optional[int] = None,
        changed_by_fk: Optional[int] = None
    ):
        self.id = id
        self.uuid = uuid
        self.user_id = user_id
        self.db_id = db_id
        self.label = label
        self.description = description
        self.python = python
        self.catalog = catalog
        self.schema = schema
        self.created_on = created_on or datetime.now()
        self.changed_on = changed_on or datetime.now()
        self.created_by_fk = created_by_fk
        self.changed_by_fk = changed_by_fk
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PythonQuery':
        """Create a PythonQuery instance from a dictionary"""
        return cls(
            id=data.get('id'),
            uuid=data.get('uuid'),
            user_id=data.get('user_id'),
            db_id=data.get('db_id'),
            label=data.get('label'),
            description=data.get('description'),
            python=data.get('python'),
            catalog=data.get('catalog'),
            schema=data.get('schema'),
            created_on=data.get('created_on'),
            changed_on=data.get('changed_on'),
            created_by_fk=data.get('created_by_fk'),
            changed_by_fk=data.get('changed_by_fk')
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert PythonQuery to dictionary"""
        result = {
            'id': self.id,
            'uuid': self.uuid,
            'user_id': self.user_id,
            'db_id': self.db_id,
            'label': self.label,
            'description': self.description,
            'python': self.python,
            'catalog': self.catalog,
            'schema': self.schema,
            'created_by_fk': self.created_by_fk,
            'changed_by_fk': self.changed_by_fk
        }
        
        # Format datetime objects
        if self.created_on:
            result['created_on'] = self.created_on.isoformat() if isinstance(self.created_on, datetime) else self.created_on
            
        if self.changed_on:
            result['changed_on'] = self.changed_on.isoformat() if isinstance(self.changed_on, datetime) else self.changed_on
            
        return result