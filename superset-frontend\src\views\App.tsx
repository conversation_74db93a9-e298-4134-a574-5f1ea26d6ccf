/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
import { Suspense, useEffect } from 'react';
import { hot } from 'react-hot-loader/root';
import {
  BrowserRouter as Router,
  Switch,
  Route,
  useLocation,
} from 'react-router-dom';
import { bindActionCreators } from 'redux';
import { css } from '@superset-ui/core';
import { GlobalStyles } from 'src/GlobalStyles';
import ErrorBoundary from 'src/components/ErrorBoundary';
import Loading from 'src/components/Loading';
import { Layout } from 'src/components';
import Menu from 'src/features/home/<USER>';
import getBootstrapData from 'src/utils/getBootstrapData';
import ToastContainer from 'src/components/MessageToasts/ToastContainer';
import setupApp from 'src/setup/setupApp';
import setupPlugins from 'src/setup/setupPlugins';
import { setupAGGridModules } from 'src/setup/setupAGGridModules';
import { routes, isFrontendRoute } from 'src/views/routes';
import { Logger, LOG_ACTIONS_SPA_NAVIGATION } from 'src/logger/LogUtils';
import setupExtensions from 'src/setup/setupExtensions';
import { logEvent } from 'src/logger/actions';
import { store } from 'src/views/store';
import { RootContextProviders } from './RootContextProviders';
import { ScrollToTop } from './ScrollToTop';
import { Toaster } from 'react-hot-toast';

import './App.css';

setupApp();
setupPlugins();
setupExtensions();
setupAGGridModules();

const bootstrapData = getBootstrapData();

let lastLocationPathname: string;

const boundActions = bindActionCreators({ logEvent }, store.dispatch);

const LocationPathnameLogger = () => {
  const location = useLocation();
  useEffect(() => {
    // This will log client side route changes for single page app user navigation
    boundActions.logEvent(LOG_ACTIONS_SPA_NAVIGATION, {
      path: location.pathname,
    });
    // reset performance logger timer start point to avoid soft navigation
    // cause dashboard perf measurement problem
    if (lastLocationPathname && lastLocationPathname !== location.pathname) {
      Logger.markTimeOrigin();
    }
    lastLocationPathname = location.pathname;
  }, [location.pathname]);
  return <></>;
};

const updatedMenuItems = { ...bootstrapData.common.menu_data };

updatedMenuItems.menu.push(
  {
    name: 'Python',
    icon: 'fa-table',
    label: 'Python',
    childs: [
      {
        name: 'PythonLab',
        icon: 'fa-table',
        label: 'PythonLab',
        url: '/python-lab/',
      },

      {
        name: 'Saved Queries',
        icon: 'fa-table',
        label: 'Saved Queries',
        url: '/python-saved-queries',
      },
    ],
  },
  {
    name: 'DataEng',
    icon: 'fa-new-icon',
    label: 'DataEng',
    childs: [
      {
        name: 'ETL Tools',
        icon: 'fa-save',
        label: 'ETL Tools',
        url: '/etl-tools/list',
      },
      {
        name: 'Datasets',
        icon: 'fa-search',
        label: 'Datasets',
        url: '/coming-soon',
      },
      {
        name: 'Curated Catalog',
        icon: 'fa-save',
        label: 'Curated Catalog',
        url: '/coming-soon',
      },
      {
        name: 'Regulation',
        icon: 'fa-save',
        label: 'Regulation',
        url: '/coming-soon',
      },
      {
        name: 'Custom',
        icon: 'fa-save',
        label: 'Custom',
        url: '/coming-soon',
      },
    ],
  },
  {
    name: 'Cognitive Service',
    icon: 'fa-table',
    label: 'Cognitive Service',
    childs: [
      {
        name: 'Pipeline',
        icon: 'fa-user',
        label: 'Pipeline',
        url: '/coming-soon',
      },
      {
        name: 'Anomaly Detection',
        icon: 'fa-group',
        label: 'Anomaly Detection',
        url: '/coming-soon',
      },
      {
        name: 'Optimisation',
        icon: 'fa-list-ol',
        label: 'Optimisation',
        url: '/coming-soon',
      },
      {
        name: 'Custom AI',
        icon: 'fa-lock',
        label: 'Custom AI',
        url: '/custom-ai/',
      },
    ],
  },
  {
    name: 'Agents & Transformers',
    icon: 'fa-table',
    label: 'Agents & Transformers',
    childs: [
      {
        name: 'Sensors',
        icon: 'fa-group',
        label: 'Sensors',
        url: '/coming-soon',
      },
      {
        name: 'Transformers',
        icon: 'fa-user',
        label: 'Transformers',
        url: '/coming-soon',
      },
      {
        name: 'Agents',
        icon: 'fa-list-ol',
        label: 'Agents',
        url: '/coming-soon',
      },
      {
        name: 'Goals',
        icon: 'fa-lock',
        label: 'Goals',
        url: '/coming-soon',
      },
      {
        name: 'Orchestration',
        icon: 'fa-lock',
        label: 'Orchestration',
        url: '/orchestration',
      },
      {
        name: 'Discovery',
        icon: 'fa-lock',
        label: 'Discovery',
        url: '/coming-soon',
      },
      {
        name: 'Autonomy',
        icon: 'fa-lock',
        label: 'Autonomy',
        url: '/coming-soon',
      },
      {
        name: 'Evolution',
        icon: 'fa-lock',
        label: 'Evolution',
        url: '/coming-soon',
      },
    ],
  },
  {
    name: 'Preferences',
    icon: 'fa-table',
    label: 'Preferences',
    childs: [
      {
        name: 'Backup',
        icon: 'fa-user',
        label: 'Backup',
        url: '/coming-soon',
      },
      {
        name: 'LLM Parameters',
        icon: 'fa-list-ol',
        label: 'LLM Parameters',
        url: '/preferences/model/',
      },
      {
        name: 'Interfaces',
        icon: 'fa-group',
        label: 'Interfaces',
        url: '/coming-soon',
      },
      {
        name: 'Customize Appearance',
        icon: 'fa-group',
        label: 'Customize Appearance',
        url: '/custom-logo',
      },
      {
        name: 'Mode',
        icon: 'fa-lock',
        label: 'Mode',
        url: '/mode',
      },
      {
        name: 'Agents',
        icon: 'fa-list-ol',
        label: 'Agents',
        url: '/coming-soon',
      },
      // {
      //   name: 'Style',
      //   icon: 'fa-lock',
      //   label: 'Style',
      //   url: '/coming-soon',
      // },
    ],
  },
  {
    name: 'Help',
    icon: 'fa-table',
    label: 'Help',
    childs: [
      {
        name: 'DDX',
        icon: 'fa-table',
        label: 'DDX',
        url: '/coming-soon',
      },
      {
        name: 'Updates',
        icon: 'fa-table',
        label: 'Updates',
        url: '/coming-soon',
      },
      {
        name: 'About',
        icon: 'fa-table',
        label: 'About',
        url: '/about',
      },
    ],
  },
);

updatedMenuItems.menu = updatedMenuItems.menu.filter(
  menuItem => menuItem.label !== 'updated-nav',
);

const App = () => (
  <Router>
    <Toaster />
    <ScrollToTop />
    <LocationPathnameLogger />
    <RootContextProviders>
      <GlobalStyles />
      <Menu data={updatedMenuItems} isFrontendRoute={isFrontendRoute} />
      <Switch>
        {routes.map(({ path, Component, props = {}, Fallback = Loading }) => (
          <Route path={path} key={path}>
            <Suspense fallback={<Fallback />}>
              <Layout.Content
                css={css`
                  display: flex;
                  flex-direction: column;
                `}
              >
                <ErrorBoundary
                  css={css`
                    margin: 16px;
                  `}
                >
                  <Component user={bootstrapData.user} {...props} />
                </ErrorBoundary>
              </Layout.Content>
            </Suspense>
          </Route>
        ))}
      </Switch>
      <ToastContainer />
    </RootContextProviders>
  </Router>
);

export default hot(App);
