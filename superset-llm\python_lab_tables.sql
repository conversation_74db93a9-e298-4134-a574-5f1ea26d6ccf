-- Create saved_python_query table
CREATE TABLE IF NOT EXISTS saved_python_query (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(36) NOT NULL,
    user_id INTEGER,
    db_id INTEGER NOT NULL,
    label VARCHAR(256) NOT NULL,
    description TEXT,
    python TEXT NOT NULL,
    catalog VARCHAR(256),
    schema VARCHAR(256),
    created_on TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    changed_on TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by_fk INTEGER,
    changed_by_fk INTEGER
);

-- Create index on uuid for faster lookups
CREATE INDEX IF NOT EXISTS idx_saved_python_query_uuid ON saved_python_query (uuid);

-- Create index on user_id for filtering by user
CREATE INDEX IF NOT EXISTS idx_saved_python_query_user_id ON saved_python_query (user_id);

-- Create index on db_id for filtering by database
CREATE INDEX IF NOT EXISTS idx_saved_python_query_db_id ON saved_python_query (db_id);